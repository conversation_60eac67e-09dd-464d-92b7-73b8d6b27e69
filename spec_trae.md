# AWX项目介绍文档

## 1. 项目概述

AWX项目是一个基于Spring Boot的后台管理系统，采用微服务架构设计，旨在为企业提供高效、安全的管理工具。系统整合了用户认证、权限管理、数据可视化等功能模块，并提供了Web前端和微信小程序两种客户端接入方式。

## 2. 功能清单

### 2.1 核心功能模块

| 模块 | 功能名称 | 功能描述 |
| --- | --- | --- |
| 用户管理 | 用户注册 | 新用户注册功能，包含手机号验证 |
| 用户管理 | 用户登录 | 用户登录认证，生成JWT token |
| 用户管理 | 用户信息管理 | 查看和修改用户基本信息 |
| 权限管理 | 角色管理 | 定义系统角色和权限 |
| 数据管理 | 数据可视化 | 展示业务数据图表 |

### 2.2 客户端功能

#### Web前端
- 后台管理界面
- 数据可视化展示
- 用户管理界面

#### 微信小程序
- 用户登录/注册
- 产品展示
- 资讯浏览
- 个人中心

## 3. 项目架构

### 3.1 系统架构图

```
前端 --> API Gateway --> 微服务 (用户服务/权限服务/数据服务) --> 数据库
```

### 3.2 技术架构

#### 后端架构
- 微服务架构
- Spring Cloud Gateway作为API网关
- Nacos用于服务注册与发现
- Sentinel用于服务熔断与限流
- MySQL作为主要数据存储

#### 前端架构
- Vue 3 + TypeScript
- Element Plus UI组件库
- Vite作为构建工具
- Pinia状态管理

#### 小程序架构
- 原生微信小程序开发

## 4. 目录结构

```
├── backend/                  # 后端代码
│   ├── backend-service/      # 后端服务模块
│   ├── common/               # 公共模块
│   │   ├── common-base/      # 基础公共模块
│   │   └── common-core/      # 核心公共模块
│   ├── gateway/              # API网关服务
│   └── sql/                  # 数据库脚本
├── frontend/                 # 前端代码
│   ├── public/               # 静态资源
│   └── src/                  # 源代码
│       ├── api/              # API接口
│       ├── assets/           # 资源文件
│       ├── components/       # 组件
│       ├── layouts/          # 布局
│       ├── router/           # 路由
│       ├── store/            # 状态管理
│       ├── utils/            # 工具类
│       └── views/            # 页面
├── miniapp/                  # 微信小程序
│   ├── images/               # 图片资源
│   ├── pages/                # 页面
│   └── utils/                # 工具类
├── docs/                     # 文档
├── nacos-configs/            # Nacos配置
└── scripts/                  # 脚本文件
    └── k8s/                  # Kubernetes部署脚本
```

## 5. 技术栈

### 5.1 后端技术栈

- **基础框架**: Spring Boot 2.7.x
- **微服务框架**: Spring Cloud
- **安全框架**: Spring Security
- **数据库**: MySQL 8.0
- **ORM框架**: MyBatis Plus 3.5.x
- **API文档**: Springdoc OpenAPI
- **认证方案**: JWT
- **服务治理**: Nacos + Sentinel
- **日志框架**: Log4j2
- **其他工具**: Lombok, Freemarker(低代码平台)

### 5.2 前端技术栈

- **框架**: Vue 3.5.x
- **构建工具**: Vite 6.3.x
- **UI组件库**: Element Plus 2.9.x
- **状态管理**: Pinia 3.0.x
- **路由**: Vue Router 4.5.x
- **HTTP客户端**: Axios 1.9.x
- **CSS预处理器**: Sass
- **语言**: TypeScript 5.8.x

### 5.3 微信小程序

- 原生微信小程序开发框架

## 6. 部署架构

项目支持多种部署方式：

1. **Kubernetes部署**
   - 使用k8s目录下的YAML文件进行部署
   - 包含后端服务、网关、前端、MySQL、Nacos、Sentinel等组件

2. **Docker部署**
   - 前端和后端均提供Dockerfile

## 7. 开发与测试

### 7.1 开发环境

- JDK 1.8
- Node.js (最新LTS版本)
- MySQL 8.0
- 微信开发者工具

### 7.2 测试工具

- JMeter用于压力测试
- 单元测试框架: Spring Boot Test

## 8. 项目特点

1. **微服务架构**: 系统采用微服务架构，各模块独立部署，提高系统可扩展性和可维护性
2. **多端支持**: 同时支持Web前端和微信小程序，满足不同场景需求
3. **服务治理**: 集成Nacos和Sentinel，提供服务注册发现、配置管理和服务熔断限流能力
4. **容器化部署**: 支持Docker和Kubernetes部署，便于系统上云和弹性扩展
5. **低代码平台**: 集成低代码开发能力，提高开发效率

## 9. 未来规划

1. 增强数据分析和可视化能力
2. 优化系统性能和用户体验
3. 增加更多客户端支持(如App)
4. 完善系统监控和告警机制