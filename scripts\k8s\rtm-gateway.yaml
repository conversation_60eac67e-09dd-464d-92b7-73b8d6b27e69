apiVersion: apps/v1
kind: Deployment
metadata:
  name: rtm-gateway-deploy
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rtm-gateway
  template:
    metadata:
      labels:
        app: rtm-gateway
    spec:
      containers:
      - name: rtm-gateway
        image: image-rtm-gateway:latest
        imagePullPolicy: Never  # 关键配置：不拉取远程镜像,从本地镜像获取
        ports:
        - containerPort: 9090
    
---

apiVersion: v1
kind: Service
metadata:
  name: rtm-gateway-service  # Service 名称
spec:
  selector:
    app: rtm-gateway  # 与 Deployment 中 Pod 的标签匹配（关键！）
  ports:
  - port: 9090        # Service 自身的端口（集群内部访问用）
    targetPort: 9090    # 映射到 Pod 中容器的端口（需与 containerPort 一致）
    nodePort: 30090   # 节点暴露的端口（范围：30000-32767，外部访问用）
  type: NodePort      # 服务类型：在每个节点上开放一个端口，供外部访问