apiVersion: apps/v1
kind: Deployment
metadata:
  name: sentinel-dashboard-deploy
spec:
  replicas: 1  # 单机模式建议先使用1个副本（多副本需集群模式）
  selector:
    matchLabels:
      app: sentinel-dashboard
  template:
    metadata:
      labels:
        app: sentinel-dashboard
    spec:
      containers:
      - name: sentinel-dashboard
        image: bladex/sentinel-dashboard:1.8.6
        ports:
        - containerPort: 8858
        
---

apiVersion: v1
kind: Service
metadata:
  name: sentinel-dashboard-service
spec:
  selector:
    app: sentinel-dashboard
  ports:
  - port: 8858
    targetPort: 8858
    nodePort: 30858
  type: NodePort
