# AWX 项目规格说明书 (Project Specification)

**版本**: 1.0  
**更新日期**: 2025-08-25  

---

## 📋 目录

1. [项目概述](#1-项目概述)
2. [功能清单](#2-功能清单)
3. [项目架构](#3-项目架构)
4. [技术栈](#4-技术栈)
5. [目录结构](#5-目录结构)
6. [部署配置](#6-部署配置)
7. [开发环境搭建](#7-开发环境搭建)
8. [API接口规范](#8-api接口规范)
9. [数据库设计](#9-数据库设计)

---

## 1. 项目概述

### 1.1 项目背景
AWX项目是一个集成了Alibaba Sentinel熔断限流功能的企业级后台管理系统，采用微服务架构设计，为企业提供高可用、高性能的管理平台解决方案。

### 1.2 项目目标
- 构建稳定可靠的微服务架构
- 集成Sentinel实现服务治理和熔断限流
- 提供完整的用户权限管理体系
- 支持低代码平台功能
- 提供小程序端和Web端多端访问

### 1.3 核心特性
- **高可用性**: 集成Sentinel熔断限流保护
- **微服务架构**: 网关服务 + 业务服务分离
- **多端支持**: Web端、小程序端
- **权限管理**: RBAC权限控制体系
- **配置中心**: Nacos统一配置管理
- **容器化部署**: Docker + Kubernetes支持

---

## 2. 功能清单

### 2.1 Web管理端功能

| 模块 | 功能名称 | 功能描述 | 优先级 |
|------|----------|----------|--------|
| 用户管理 | 用户注册 | 支持手机号注册，包含验证码验证 | 高 |
| 用户管理 | 用户登录 | JWT Token认证登录 | 高 |
| 用户管理 | 用户信息管理 | 查看、编辑用户基本信息 | 高 |
| 用户管理 | 用户列表 | 分页查询用户列表 | 高 |
| 权限管理 | 角色管理 | 创建、编辑、删除角色 | 高 |
| 权限管理 | 菜单管理 | 菜单树结构管理 | 高 |
| 权限管理 | 角色菜单关联 | 为角色分配菜单权限 | 高 |
| 权限管理 | 用户角色关联 | 为用户分配角色 | 高 |
| 会员管理 | 会员信息管理 | 会员数据的增删改查 | 中 |
| 产品管理 | 产品信息管理 | 产品数据维护 | 中 |
| 内容管理 | 新闻管理 | 新闻资讯管理 | 中 |
| 低代码平台 | 动态表单 | 支持动态生成表单和接口 | 中 |
| 愿望管理 | 愿望清单 | 用户愿望信息管理 | 低 |

### 2.2 小程序端功能

| 模块 | 页面/功能 | 功能描述 |
|------|-----------|----------|
| 首页 | 首页展示 | 轮播图、产品推荐、新闻资讯 |
| 产品 | 产品列表 | 产品展示和详情查看 |
| 解决方案 | 方案列表 | 解决方案展示和详情 |
| 新闻 | 新闻列表 | 新闻资讯浏览 |
| 个人中心 | 用户信息 | 个人信息管理、登录注册 |

### 2.3 系统管理功能

| 功能类别 | 功能名称 | 功能描述 |
|----------|----------|----------|
| 监控管理 | Sentinel监控 | 实时监控服务状态和限流情况 |
| 配置管理 | Nacos配置 | 统一配置管理和动态更新 |
| 服务治理 | 服务发现 | 自动服务注册发现 |
| 熔断限流 | 流量控制 | QPS和并发线程数限流 |
| 熔断限流 | 熔断降级 | 异常比例和响应时间熔断 |

---

## 3. 项目架构

### 3.1 整体架构图

```mermaid
graph TB
    A[前端Web应用<br/>Vue.js + Element Plus] --> B[API网关<br/>Spring Cloud Gateway]
    C[微信小程序] --> B
    
    B --> D[后端业务服务<br/>Spring Boot]
    
    D --> E[MySQL数据库]
    
    B --> F[Sentinel限流组件]
    D --> F
    
    F --> G[Sentinel控制台<br/>监控管理]
    
    B --> H[Nacos配置中心]
    D --> H
    
    H --> I[配置持久化]
    
    subgraph "Kubernetes集群"
        B
        D
        G
        H
    end
```

### 3.2 微服务架构

| 服务名称 | 端口 | 职责描述 |
|----------|------|----------|
| Gateway Service | 9090 | API网关，路由转发、限流、认证 |
| Backend Service | 8080 | 业务服务，核心业务逻辑处理 |
| Sentinel Dashboard | 8080 | 限流监控控制台 |
| Nacos Server | 8848 | 配置中心和服务发现 |

### 3.3 技术架构层次

```
┌─────────────────────────────────────────────────┐
│                 前端层                          │
│  Vue.js + Element Plus + TypeScript            │
└─────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────┐
│                 网关层                          │
│  Spring Cloud Gateway + Sentinel              │
└─────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────┐
│                 业务层                          │
│  Spring Boot + Spring Security + MyBatis Plus │
└─────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────┐
│                 数据层                          │
│  MySQL + Redis (可选)                         │
└─────────────────────────────────────────────────┘
```

---

## 4. 技术栈

### 4.1 后端技术栈

| 技术领域 | 技术选型 | 版本 | 说明 |
|----------|----------|------|------|
| 核心框架 | Spring Boot | 2.7.18 | 微服务核心框架 |
| 微服务治理 | Spring Cloud | 2021.0.8 | 微服务组件套件 |
| 网关服务 | Spring Cloud Gateway | - | API网关 |
| 服务发现 | Nacos Discovery | 2021.0.5.0 | 服务注册发现 |
| 配置管理 | Nacos Config | 2021.0.5.0 | 配置中心 |
| 熔断限流 | Alibaba Sentinel | 1.8.6 | 服务保护 |
| 数据库 | MySQL | 8.0.33 | 关系型数据库 |
| ORM框架 | MyBatis Plus | 3.5.3.2 | 数据访问层 |
| 安全框架 | Spring Security | - | 认证授权 |
| JWT | JJWT | 0.11.5 | Token生成解析 |
| API文档 | SpringDoc OpenAPI | 1.6.15 | 接口文档 |
| 模板引擎 | FreeMarker | 2.3.31 | 低代码模板 |
| JSON处理 | FastJSON | 1.2.83 | JSON序列化 |
| 工具库 | Lombok | - | 代码简化 |
| 构建工具 | Maven | 3.5+ | 项目构建 |
| JDK版本 | Java | 1.8 | 运行环境 |

### 4.2 前端技术栈

| 技术领域 | 技术选型 | 版本 | 说明 |
|----------|----------|------|------|
| 前端框架 | Vue.js | 3.5.13 | 渐进式框架 |
| 开发语言 | TypeScript | 5.8.3 | 类型安全 |
| 构建工具 | Vite | 6.3.5 | 快速构建 |
| UI组件库 | Element Plus | 2.9.11 | 企业级UI组件 |
| 状态管理 | Pinia | 3.0.2 | 状态管理 |
| 路由管理 | Vue Router | 4.5.1 | 前端路由 |
| HTTP客户端 | Axios | 1.9.0 | 请求库 |
| CSS预处理 | Sass | 1.89.1 | CSS扩展 |

### 4.3 小程序技术栈

| 技术领域 | 技术选型 | 说明 |
|----------|----------|------|
| 开发框架 | 微信小程序原生 | 官方框架 |
| 开发语言 | JavaScript | - |
| 样式语言 | WXSS | 小程序样式 |
| 数据请求 | wx.request | 原生请求API |

### 4.4 部署运维技术栈

| 技术领域 | 技术选型 | 说明 |
|----------|----------|------|
| 容器化 | Docker | 应用容器化 |
| 容器编排 | Kubernetes | 容器集群管理 |
| 服务网格 | Nginx | 负载均衡 |
| 监控告警 | Sentinel Dashboard | 服务监控 |

---

## 5. 目录结构

### 5.1 整体目录结构

```
d:\code\awx\
├── backend/                    # 后端服务目录
│   ├── gateway/               # 网关服务
│   ├── backend-service/       # 业务服务
│   ├── common/               # 公共组件
│   │   └── common-core/      # 核心公共模块
│   ├── sql/                  # 数据库脚本
│   └── pom.xml              # 父级Maven配置
├── frontend/                 # 前端Web应用
│   ├── src/                 # 源码目录
│   ├── package.json         # 前端依赖配置
│   └── vite.config.ts       # Vite构建配置
├── miniapp/                 # 微信小程序
│   ├── pages/               # 小程序页面
│   ├── utils/               # 工具类
│   └── app.json            # 小程序配置
├── nacos-configs/           # Nacos配置文件
├── scripts/                 # 部署脚本
│   └── k8s/                # Kubernetes配置
└── 文档文件                # 项目文档
```

### 5.2 后端服务详细结构

```
backend/backend-service/src/main/java/com/dz/ms/
├── BackendApplication.java     # 启动类
├── config/                    # 配置类
├── controller/                # 控制器层
│   ├── AuthController.java    # 认证控制器
│   ├── UserController.java    # 用户管理
│   ├── MemberController.java  # 会员管理
│   ├── MenuController.java    # 菜单管理
│   ├── RoleController.java    # 角色管理
│   ├── ProductController.java # 产品管理
│   └── ...                   # 其他控制器
├── entity/                   # 实体类
├── dto/                     # 数据传输对象
├── vo/                      # 视图对象
├── service/                 # 服务层
├── mapper/                  # 数据访问层
├── dynamic/                 # 低代码模块
├── lowcode/                 # 低代码平台
└── util/                    # 工具类
```

### 5.3 前端应用详细结构

```
frontend/src/
├── api/                     # API接口定义
│   ├── auth.ts             # 认证接口
│   ├── user.ts             # 用户接口
│   ├── member.ts           # 会员接口
│   └── ...                 # 其他业务接口
├── components/              # 公共组件
├── views/                   # 页面组件
├── router/                  # 路由配置
├── store/                   # 状态管理
│   ├── index.ts            # 根存储
│   ├── menu.ts             # 菜单状态
│   └── permission.ts       # 权限状态
├── utils/                   # 工具函数
├── directives/              # 自定义指令
└── layouts/                 # 布局组件
```

---

## 6. 部署配置

### 6.1 Docker部署

#### 网关服务 Dockerfile
```dockerfile
FROM openjdk:8-jre-alpine
VOLUME /tmp
COPY target/gateway-1.0.0.jar app.jar
EXPOSE 9090
ENTRYPOINT ["java","-jar","/app.jar"]
```

#### 业务服务 Dockerfile
```dockerfile
FROM openjdk:8-jre-alpine
VOLUME /tmp
COPY target/backend-service-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java","-jar","/app.jar"]
```

### 6.2 Kubernetes部署

| 服务 | 配置文件 | 说明 |
|------|----------|------|
| 网关服务 | rtm-gateway.yaml | Gateway服务K8s配置 |
| 业务服务 | rtm-backend.yaml | Backend服务K8s配置 |
| 前端应用 | rtm-web.yaml | Web前端K8s配置 |
| MySQL | mysql5744.yaml | 数据库K8s配置 |
| Nacos | nacos-deploy.yaml | 配置中心K8s配置 |
| Sentinel | sentinel-dashboard.yaml | 监控服务K8s配置 |

### 6.3 服务端口配置

| 服务 | 端口 | 协议 | 访问地址 |
|------|------|------|----------|
| Gateway | 9090 | HTTP | http://localhost:9090 |
| Backend | 8080 | HTTP | http://localhost:8080 |
| Frontend | 5173 | HTTP | http://localhost:5173 |
| Nacos | 8848 | HTTP | http://localhost:8848/nacos |
| Sentinel | 8080 | HTTP | http://localhost:8080 |
| MySQL | 3306 | TCP | localhost:3306 |

---

## 7. 开发环境搭建

### 7.1 环境要求

| 工具/环境 | 版本要求 | 说明 |
|-----------|----------|------|
| JDK | 1.8+ | Java开发环境 |
| Maven | 3.5+ | 项目构建工具 |
| Node.js | 16+ | 前端开发环境 |
| MySQL | 8.0+ | 数据库服务 |
| Docker | 20+ | 容器化部署(可选) |

### 7.2 启动步骤

#### 7.2.1 启动基础服务

```bash
# 1. 启动Nacos
cd scripts/
./start-nacos.sh

# 2. 启动Sentinel Dashboard
./start-sentinel-dashboard.sh
```

#### 7.2.2 配置Nacos规则

在Nacos控制台配置以下规则：
- backend-service-flow-rules
- backend-service-degrade-rules  
- gateway-service-gw-flow-rules

#### 7.2.3 启动应用服务

```bash
# 3. 启动网关服务
cd backend/gateway
mvn spring-boot:run

# 4. 启动业务服务
cd backend/backend-service
mvn spring-boot:run

# 5. 启动前端应用
cd frontend
npm install
npm run dev
```

### 7.3 数据库初始化

```bash
# 执行数据库脚本
mysql -u root -p < database_schema.sql
mysql -u root -p < backend/sql/init.sql
```

---

## 8. API接口规范

### 8.1 接口基础规范

#### 8.1.1 请求格式
- 请求协议：HTTP/HTTPS
- 请求方法：GET, POST, PUT, DELETE
- 内容类型：application/json
- 编码格式：UTF-8

#### 8.1.2 响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2025-08-25T10:30:00"
}
```

### 8.2 核心接口清单

#### 8.2.1 认证接口

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 用户登录 | POST | /auth1/login | 用户认证登录 |
| 用户注册 | POST | /auth1/register | 新用户注册 |
| 刷新Token | POST | /auth1/refresh | 刷新访问令牌 |
| 用户登出 | POST | /auth1/logout | 用户退出登录 |

#### 8.2.2 用户管理接口

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 用户列表 | GET | /api/user/page | 分页查询用户 |
| 用户详情 | GET | /api/user/{id} | 获取用户信息 |
| 创建用户 | POST | /api/user | 创建新用户 |
| 更新用户 | PUT | /api/user/{id} | 更新用户信息 |
| 删除用户 | DELETE | /api/user/{id} | 删除用户 |

#### 8.2.3 权限管理接口

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 菜单树 | GET | /api/menu/tree | 获取菜单树 |
| 角色列表 | GET | /api/role/list | 获取角色列表 |
| 用户权限 | GET | /api/user/{id}/permissions | 获取用户权限 |

### 8.3 接口认证

- 认证方式：JWT Bearer Token
- Token位置：HTTP Header
- 格式：`Authorization: Bearer <token>`
- 过期时间：24小时

---

## 9. 数据库设计

### 9.1 核心数据表

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| user | 用户表 | id, phone, password, nickname, avatar |
| role | 角色表 | id, name, description |
| menu | 菜单表 | id, name, path, parent_id |
| user_role | 用户角色关联 | user_id, role_id |
| role_menu | 角色菜单关联 | role_id, menu_id |
| member | 会员表 | id, name, level, points |
| product | 产品表 | id, title, desc, image_url |
| news | 新闻表 | id, title, content, category |

### 9.2 数据库关系图

```mermaid
erDiagram
    USER ||--o{ USER_ROLE : has
    ROLE ||--o{ USER_ROLE : assigned
    ROLE ||--o{ ROLE_MENU : has
    MENU ||--o{ ROLE_MENU : belongs
    USER {
        bigint id PK
        varchar phone UK
        varchar password
        varchar nickname
        varchar avatar
        datetime created_at
    }
    ROLE {
        bigint id PK
        varchar name
        varchar description
    }
    MENU {
        bigint id PK
        varchar name
        varchar path
        bigint parent_id FK
    }
```

### 9.3 数据字典

#### 9.3.1 用户状态枚举
- 0: 禁用
- 1: 启用

#### 9.3.2 菜单类型枚举
- M: 目录
- C: 菜单
- F: 按钮

---

## 📚 附录

### A. 相关文档链接
- [Sentinel部署指南](README-Sentinel.md)
- [功能规格说明书](AWX项目功能规格说明书(FSD).md)
- [测试用例文档](MiniAppAuthController_TestCases.md)

### B. 联系信息
- 项目维护者：开发团队
- 技术支持：技术部门
- 文档更新：项目经理

### C. 更新日志
- v1.0 (2025-08-25): 初始版本，完整项目规格说明

---

**文档说明**: 本文档为AWX项目的完整规格说明，涵盖了项目的功能、架构、技术栈等各个方面。如有疑问或需要更新，请联系项目维护团队。