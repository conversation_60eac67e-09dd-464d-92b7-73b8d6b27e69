# AWX 项目介绍文档

## 📋 项目概述

AWX 是一个基于微服务架构的企业级应用平台，包含后端服务、前端管理系统和小程序端。项目采用 Spring Boot + Vue.js + 微信小程序的现代化技术栈，提供完整的用户管理、权限控制、产品管理、解决方案、新闻资讯等核心功能。

### 🎯 项目目标
- 构建高可用、可扩展的微服务架构
- 提供完整的用户权限管理体系
- 支持低代码平台开发
- 实现多端统一管理（Web端 + 小程序端）

## 🚀 功能清单

### 1. 用户权限管理
- **用户管理**：用户注册、登录、信息维护
- **角色管理**：角色创建、权限分配、角色继承
- **菜单管理**：动态菜单配置、权限控制
- **权限控制**：基于角色的访问控制（RBAC）

### 2. 产品管理
- **产品信息**：产品标题、描述、图标、图片管理
- **产品特性**：支持JSON格式的产品特性配置
- **产品分类**：灵活的产品分类体系

### 3. 解决方案管理
- **方案配置**：解决方案标题、描述、图标管理
- **标签系统**：支持多标签分类
- **内容管理**：富文本内容编辑

### 4. 新闻资讯
- **新闻发布**：新闻标题、摘要、内容管理
- **分类管理**：新闻分类体系
- **浏览量统计**：访问量统计功能

### 5. 用户反馈
- **反馈收集**：用户意见收集
- **联系方式**：用户联系信息管理

### 6. 低代码平台
- **动态编译**：支持代码动态编译
- **模板引擎**：基于FreeMarker的模板系统
- **代码生成**：自动化代码生成

### 7. 系统监控
- **流量控制**：基于Sentinel的限流熔断
- **服务监控**：服务健康状态监控
- **日志管理**：完整的日志记录体系

## 🏗️ 项目架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端管理系统  │    │   微信小程序    │    │   移动端H5      │
│   (Vue.js)      │    │   (WeChat)      │    │   (Vue.js)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                    ┌─────────────────────────┐
                    │      API网关            │
                    │   (Spring Cloud Gateway)│
                    └─────────────────────────┘
                                 │
                                 ▼
                    ┌─────────────────────────┐
                    │     后端服务            │
                    │   (Backend Service)     │
                    └─────────────────────────┘
                                 │
                    ┌─────────────────────────┐
                    │     公共模块            │
                    │   (Common Modules)      │
                    └─────────────────────────┘
                                 │
                    ┌─────────────────────────┐
                    │     数据存储            │
                    │   (MySQL + Redis)       │
                    └─────────────────────────┘
```

### 微服务架构
- **网关服务**：Spring Cloud Gateway，负责路由转发、限流熔断
- **后端服务**：Spring Boot应用，提供业务逻辑处理
- **配置中心**：Nacos，统一配置管理
- **服务发现**：Nacos，服务注册与发现
- **限流熔断**：Sentinel，流量控制和熔断降级

## 📁 目录结构

### 后端项目结构
```
backend/
├── common/                          # 公共模块
│   ├── common-base/                # 基础模块
│   └── common-core/                # 核心模块
├── gateway/                        # API网关服务
│   ├── src/main/java/
│   │   └── com/dz/ms/gateway/
│   ├── pom.xml
│   └── deploy/
├── backend-service/                # 后端业务服务
│   ├── src/main/java/
│   │   └── com/dz/ms/
│   │       ├── controller/         # 控制器层
│   │       ├── service/           # 服务层
│   │       ├── mapper/            # 数据访问层
│   │       ├── entity/            # 实体类
│   │       ├── dto/               # 数据传输对象
│   │       ├── vo/                # 视图对象
│   │       ├── util/              # 工具类
│   │       ├── lowcode/           # 低代码平台
│   │       ├── filter/            # 过滤器
│   │       ├── dynamic/           # 动态编译
│   │       ├── config/            # 配置类
│   │       └── common/            # 公共组件
│   ├── src/main/resources/
│   ├── pom.xml
│   └── deploy/
├── sql/                           # 数据库脚本
└── pom.xml                        # 父POM文件
```

### 前端项目结构
```
frontend/
├── src/
│   ├── api/                       # API接口定义
│   ├── assets/                    # 静态资源
│   ├── components/                # 公共组件
│   ├── layouts/                   # 布局组件
│   ├── router/                    # 路由配置
│   ├── store/                     # 状态管理
│   ├── styles/                    # 样式文件
│   ├── utils/                     # 工具函数
│   ├── views/                     # 页面组件
│   │   ├── Auth/                  # 认证相关页面
│   │   ├── User/                  # 用户管理页面
│   │   ├── Role/                  # 角色管理页面
│   │   ├── Menu/                  # 菜单管理页面
│   │   ├── Member/                # 会员管理页面
│   │   ├── Wish/                  # 愿望管理页面
│   │   ├── LowCode/               # 低代码平台页面
│   │   ├── Dynamic/               # 动态功能页面
│   │   ├── Welcome/               # 欢迎页面
│   │   └── My/                    # 个人中心页面
│   ├── main.ts                    # 主入口文件
│   ├── App.vue                    # 根组件
│   └── style.css                  # 全局样式
├── public/                        # 公共资源
├── dist/                          # 构建输出目录
├── package.json                   # 依赖配置
├── vite.config.ts                 # Vite配置
├── tsconfig.json                  # TypeScript配置
├── nginx.conf                     # Nginx配置
├── Dockerfile                     # Docker配置
└── deploy.bat                     # 部署脚本
```

### 小程序项目结构
```
miniapp/
├── pages/                         # 页面目录
│   ├── index/                     # 首页
│   ├── login/                     # 登录页
│   ├── product/                   # 产品页
│   ├── solution/                  # 解决方案页
│   ├── news/                      # 新闻页
│   └── profile/                   # 个人中心页
├── utils/                         # 工具函数
├── images/                        # 图片资源
├── app.js                         # 小程序入口文件
├── app.json                       # 小程序配置文件
├── app.wxss                       # 全局样式文件
└── project.config.json            # 项目配置文件
```

### 配置文件结构
```
nacos-configs/                     # Nacos配置中心
├── rtm-gateway.yaml              # 网关配置
├── gateway-service-gw-api-group-rules.json    # 网关API分组规则
├── gateway-service-gw-flow-rules.json         # 网关流控规则
├── backend-service-system-rules.json          # 后端服务系统规则
├── backend-service-degrade-rules.json         # 后端服务熔断规则
└── backend-service-flow-rules.json            # 后端服务流控规则
```

## 🛠️ 技术栈

### 后端技术栈
- **核心框架**：Spring Boot 2.7.18
- **微服务框架**：Spring Cloud 2021.0.8
- **服务注册与配置**：Nacos 2021.0.5.0
- **网关**：Spring Cloud Gateway
- **限流熔断**：Alibaba Sentinel
- **数据访问**：MyBatis-Plus *******
- **数据库**：MySQL 8.0.33
- **安全框架**：Spring Security + JWT
- **API文档**：SpringDoc OpenAPI 1.6.15
- **模板引擎**：FreeMarker 2.3.31
- **JSON处理**：FastJSON 1.2.83
- **工具库**：Lombok, Hutool
- **日志框架**：Log4j2
- **构建工具**：Maven

### 前端技术栈
- **核心框架**：Vue.js 3.x
- **开发语言**：TypeScript
- **构建工具**：Vite
- **状态管理**：Vuex/Pinia
- **路由管理**：Vue Router
- **UI组件库**：Element Plus
- **HTTP客户端**：Axios
- **样式预处理**：CSS/SCSS
- **代码规范**：ESLint + Prettier
- **部署工具**：Nginx, Docker

### 小程序技术栈
- **开发框架**：微信小程序原生框架
- **开发语言**：JavaScript
- **样式语言**：WXSS
- **配置语言**：JSON

### 基础设施
- **服务注册与发现**：Nacos
- **配置中心**：Nacos
- **限流熔断**：Sentinel
- **数据库**：MySQL
- **缓存**：Redis（可选）
- **反向代理**：Nginx
- **容器化**：Docker
- **监控**：Sentinel Dashboard

## 🔧 核心功能模块

### 1. 认证授权模块
- JWT Token认证
- 基于角色的权限控制（RBAC）
- 动态菜单权限
- 接口级权限控制

### 2. 用户管理模块
- 用户注册登录
- 用户信息管理
- 用户角色分配
- 密码加密存储

### 3. 低代码平台
- 动态代码编译
- 模板引擎支持
- 代码生成器
- 可视化配置

### 4. 系统监控
- 服务健康检查
- 流量监控
- 熔断降级
- 日志追踪

## 🚀 部署说明

### 环境要求
- **JDK**：1.8+
- **Maven**：3.6+
- **Node.js**：16+
- **MySQL**：8.0+
- **Nacos**：2.0+
- **Sentinel**：1.8+

### 启动顺序
1. 启动Nacos服务
2. 启动Sentinel控制台
3. 启动后端服务
4. 启动网关服务
5. 启动前端服务

### 部署方式
- **开发环境**：本地开发模式
- **测试环境**：Docker容器化部署
- **生产环境**：Kubernetes集群部署

## 📊 项目特色

### 1. 微服务架构
- 服务解耦，独立部署
- 高可用性和可扩展性
- 技术栈灵活选择

### 2. 低代码平台
- 支持动态代码编译
- 模板化开发
- 快速原型构建

### 3. 完善的监控体系
- 实时流量监控
- 智能熔断降级
- 完整的日志追踪

### 4. 多端支持
- Web管理端
- 微信小程序端
- 移动端H5

## 🔮 未来规划

### 短期目标
- 完善单元测试覆盖
- 优化性能监控
- 增强安全防护

### 中期目标
- 引入消息队列
- 集成分布式事务
- 支持多租户架构

### 长期目标
- 云原生架构升级
- 智能化运维
- 国际化支持

---

*本文档基于项目当前状态编写，如有更新请及时同步。*
