**AWX项目功能规格说明书(FSD)**

Version 1.0

2025-08-06

**Summary**

|     |     |
| --- | --- |
| RFC Code | AWX-2025-001 |
| RFC Description | AWX项目功能规格说明书 |
| System Name: | AWX系统 |
| Current Version: | 1.0 |
| Vender Name: | 腾讯云团队 |
| RFC type: | ☒New function ☐ Change function<br><br>☐ Bug fix ☐ Optimize performance |
| Estimated Man-day: | 30 |
| Plan Go live Date: | 2025-10-01 |
| Authors: <AUTHORS>
| Distribution: | 产品、开发、测试、运维团队 |

**Revision History**

|     |     |
| --- | --- |
| Version, Date | Change Description |
| Version 1.0 | 初始版本 |

**Table of Contents**

1. 引言与背景
2. 功能概述
3. 利益相关方
4. 工作量分解与计划
5. 签署确认

**1. 引言与背景**

**1.1 背景**

AWX项目是一个基于Spring Boot的后台管理系统，旨在为企业提供高效、安全的管理工具。项目采用微服务架构，整合了用户认证、权限管理、数据可视化等功能模块。

**1.2 目的与范围**

本文档旨在定义AWX系统的功能规格，包括系统功能模块、接口规范、数据流等。文档范围涵盖系统所有核心功能模块。

**2. 功能概述**

**2.1 系统架构图**

```mermaid
graph TD
    A[前端] --> B[API Gateway]
    B --> C[用户服务]
    B --> D[权限服务]
    B --> E[数据服务]
    C --> F[MySQL]
    D --> F
    E --> F
```

**2.2 功能清单**

| 模块 | 功能名称 | 功能描述 |
| --- | --- | --- |
| 用户管理 | 用户注册 | 新用户注册功能，包含手机号验证 |
| 用户管理 | 用户登录 | 用户登录认证，生成JWT token |
| 用户管理 | 用户信息管理 | 查看和修改用户基本信息 |
| 权限管理 | 角色管理 | 定义系统角色和权限 |
| 数据管理 | 数据可视化 | 展示业务数据图表 |

**2.3 系统与业务流程**

**用户注册流程**
```mermaid
graph TD
    A[用户填写注册信息] --> B[发送验证码]
    B --> C[验证手机号]
    C --> D[创建用户账号]
    D --> E[返回注册结果]
```

**2.4 程序/代码清单**

| 功能模块 | 程序名 | 程序说明 |
| --- | --- | --- |
| 用户认证 | MiniAppAuthController | 处理用户注册、登录等认证逻辑 |
| 用户管理 | UserController | 管理用户信息 |
| 权限管理 | RoleController | 管理角色和权限 |

**2.5 接口/API技术规范**

**用户注册接口**
- 路径: /auth1/register
- 方法: POST
- 请求体:
```json
{
  "phone": "string",
  "password": "string",
  "confirmPassword": "string",
  "verificationCode": "string",
  "nickname": "string",
  "avatar": "string"
}
```
- 响应:
```json
{
  "code": "number",
  "message": "string",
  "data": "string"
}
```

**2.6 数据流与数据字典**

**用户表(user)**
| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | bigint | 主键ID |
| phone | varchar(20) | 手机号 |
| password | varchar(100) | 密码(加密) |
| nickname | varchar(50) | 昵称 |
| avatar | varchar(255) | 头像URL |

**2.7 原型图/Mock up**

![注册页面](UI/首页 - 注册登录@1x.png)
![用户管理](UI/我的@1x.png)

**3. 利益相关方**

| 姓名 | 部门/团队 | 职位 | 职责 |
| --- | --- | --- | --- |
| 张三 | 产品部 | 产品经理 | 需求确认 |
| 李四 | 开发部 | 技术负责人 | 技术实现 |
| 王五 | 测试部 | 测试经理 | 质量保证 |

**4. 工作量分解与计划**

| 类别 | 项目 | 人天 | 开始日期 | 结束日期 | 参与人 |
| --- | --- | --- | --- | --- | --- |
| 开发 | 用户管理模块 | 10 | 2025-08-10 | 2025-08-20 | 开发团队 |
| 开发 | 权限管理模块 | 8 | 2025-08-15 | 2025-08-25 | 开发团队 |
| 测试 | 系统测试 | 5 | 2025-08-25 | 2025-08-30 | 测试团队 |
| 上线 | 系统部署 | 2 | 2025-09-30 | 2025-10-01 | 运维团队 |

**5. 签署确认**

|     |     |     |
| --- | --- | --- |
| 业务负责人 | 张三 | 2025-08-06 |
| 技术负责人 | 李四 | 2025-08-06 |
| 测试负责人 | 王五 | 2025-08-06 |