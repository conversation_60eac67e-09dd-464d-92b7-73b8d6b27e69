# AWX 项目介绍文档

## 📋 项目概述

AWX 是一个基于 Spring Boot 和 Vue.js 的企业级后台管理系统，采用微服务架构设计，集成了用户认证、权限管理、内容管理、低代码平台等功能模块。项目同时提供了微信小程序端，为企业提供全方位的数字化管理解决方案。

### 基本信息
- **项目名称**: AWX 系统
- **版本**: 1.0.0
- **开发团队**: 腾讯云团队
- **预计上线**: 2025-10-01
- **开发周期**: 30 人天

## 🎯 功能清单

### 核心功能模块

| 模块分类 | 功能名称 | 功能描述 | 实现状态 |
|---------|---------|---------|---------|
| **用户管理** | 用户注册 | 支持手机号注册，短信验证码验证 | ✅ |
| | 用户登录 | JWT Token认证，支持小程序和Web登录 | ✅ |
| | 用户信息管理 | 个人资料修改、头像上传 | ✅ |
| | 密码管理 | 密码修改、找回密码 | ✅ |
| **权限管理** | 角色管理 | 角色创建、编辑、删除、权限分配 | ✅ |
| | 菜单管理 | 动态菜单配置、权限控制 | ✅ |
| | 用户角色关联 | 用户角色绑定、权限继承 | ✅ |
| **内容管理** | 产品管理 | 产品信息展示、特性管理 | ✅ |
| | 解决方案 | 方案展示、标签分类 | ✅ |
| | 新闻资讯 | 新闻发布、分类管理、浏览统计 | ✅ |
| | 轮播图管理 | 首页Banner管理 | ✅ |
| **低代码平台** | 元数据管理 | 实体定义、字段配置 | ✅ |
| | 代码生成 | 自动生成CRUD代码 | ✅ |
| | 动态表单 | 表单配置、数据录入 | ✅ |
| **系统管理** | 会员管理 | 会员信息、审核状态 | ✅ |
| | 反馈管理 | 用户反馈收集、处理 | ✅ |
| | 系统监控 | 服务状态、性能监控 | ✅ |

### 小程序功能

| 页面 | 功能描述 |
|-----|---------|
| 首页 | 产品展示、解决方案、新闻资讯 |
| 产品 | 产品列表、详情展示 |
| 解决方案 | 方案分类、详细介绍 |
| 新闻 | 资讯列表、内容浏览 |
| 我的 | 个人中心、设置管理 |

## 🏗️ 项目架构

### 系统架构图

```mermaid
graph TD
    A[微信小程序] --> B[API Gateway]
    C[Web管理后台] --> B
    B --> D[后端服务]
    D --> E[MySQL数据库]
    D --> F[Nacos配置中心]
    D --> G[Sentinel熔断限流]
    
    subgraph "后端服务"
        D1[用户服务]
        D2[权限服务]
        D3[内容服务]
        D4[低代码服务]
    end
    
    D --> D1
    D --> D2
    D --> D3
    D --> D4
```

### 技术架构

#### 后端技术栈
- **框架**: Spring Boot 2.7.18
- **数据库**: MySQL 8.0
- **ORM**: MyBatis Plus 3.5.3
- **安全**: Spring Security + JWT
- **文档**: Springdoc OpenAPI 1.6.15
- **微服务**: Spring Cloud Alibaba
- **注册中心**: Nacos Discovery
- **配置中心**: Nacos Config
- **熔断限流**: Alibaba Sentinel
- **构建工具**: Maven

#### 前端技术栈
- **框架**: Vue.js 3.5.13
- **构建工具**: Vite 6.3.5
- **UI组件**: Element Plus 2.9.11
- **状态管理**: Pinia 3.0.2
- **路由**: Vue Router 4.5.1
- **HTTP客户端**: Axios 1.9.0
- **开发语言**: TypeScript 5.8.3

#### 小程序技术栈
- **平台**: 微信小程序
- **开发工具**: 微信开发者工具
- **UI框架**: 原生小程序组件

## 📁 目录结构

### 项目根目录
```
awx/
├── backend/                    # 后端服务
│   ├── backend-service/       # 主要业务服务
│   ├── gateway/              # API网关
│   ├── common/               # 公共模块
│   └── pom.xml              # Maven父项目配置
├── frontend/                  # Web管理后台
│   ├── src/                  # 源代码
│   ├── public/               # 静态资源
│   └── package.json         # 依赖配置
├── miniapp/                   # 微信小程序
│   ├── pages/               # 页面文件
│   ├── utils/               # 工具函数
│   └── app.json            # 小程序配置
├── docs/                     # 项目文档
├── scripts/                  # 部署脚本
├── nacos-configs/           # Nacos配置文件
└── database_schema.sql      # 数据库结构
```

### 后端详细结构
```
backend/backend-service/src/main/java/com/dz/ms/
├── BackendApplication.java     # 启动类
├── config/                    # 配置类
├── controller/                # 控制器层
│   ├── AuthController.java    # 认证控制器
│   ├── UserController.java    # 用户管理
│   ├── MemberController.java  # 会员管理
│   ├── MenuController.java    # 菜单管理
│   ├── RoleController.java    # 角色管理
│   └── ProductController.java # 产品管理
├── entity/                   # 实体类
├── dto/                     # 数据传输对象
├── vo/                      # 视图对象
├── service/                 # 服务层
├── mapper/                  # 数据访问层
├── dynamic/                 # 低代码模块
├── lowcode/                 # 低代码平台
└── util/                    # 工具类
```

### 前端详细结构
```
frontend/src/
├── api/                     # API接口
├── assets/                  # 静态资源
├── components/              # 公共组件
├── directives/              # 自定义指令
├── layouts/                 # 布局组件
├── router/                  # 路由配置
├── store/                   # 状态管理
├── utils/                   # 工具函数
└── views/                   # 页面组件
    ├── Auth/               # 认证页面
    ├── User/               # 用户管理
    ├── Role/               # 角色管理
    ├── Menu/               # 菜单管理
    ├── Member/             # 会员管理
    └── LowCode/            # 低代码平台
```

## 🗄️ 数据库设计

### 核心数据表

| 表名 | 说明 | 主要字段 |
|-----|------|---------|
| user | 用户表 | id, phone, password, nickname, avatar |
| product | 产品表 | id, title, desc, icon, image_url, features |
| solution | 解决方案表 | id, title, desc, icon, tags, content |
| news | 新闻资讯表 | id, title, summary, content, cover_image, category |
| feedback | 用户反馈表 | id, user_id, content, contact |
| role | 角色表 | id, name, description, permissions |
| menu | 菜单表 | id, name, path, component, permission |
| meta_entity | 元数据实体表 | id, name, table_name, description |
| meta_field | 元数据字段表 | id, entity_id, field_name, field_type |

### 数据库特性
- 统一的审计字段：created_by, updated_by, created_at, updated_at
- 支持逻辑删除
- 字符集：UTF-8
- 时区：Asia/Shanghai

## 🔧 核心特性

### 1. 微服务架构
- 基于 Spring Cloud Alibaba 构建
- 服务注册与发现（Nacos）
- 配置中心（Nacos Config）
- API网关（Spring Cloud Gateway）

### 2. 安全认证
- JWT Token 认证机制
- Spring Security 权限控制
- 基于角色的访问控制（RBAC）
- 动态菜单权限

### 3. 熔断限流
- Alibaba Sentinel 集成
- 流量控制和熔断降级
- 系统负载保护
- 实时监控面板

### 4. 低代码平台
- 可视化表单设计
- 动态代码生成
- 元数据管理
- 自动化CRUD操作

## 🚀 部署说明

### 环境要求
- JDK 1.8+
- Node.js 16+
- MySQL 8.0+
- Nacos 2.x
- Redis（可选）

### 快速启动
1. 启动基础服务（Nacos、MySQL）
2. 导入数据库结构（database_schema.sql）
3. 启动后端服务
4. 启动前端应用
5. 部署微信小程序

### 监控地址
- Nacos控制台: http://localhost:8848/nacos
- Sentinel控制台: http://localhost:8080
- API文档: http://localhost:9090/swagger-ui.html

## 📚 相关文档

- [功能规格说明书](AWX项目功能规格说明书(FSD).md)
- [Sentinel部署指南](README-Sentinel.md)
- [压力测试指南](docs/jmeter-pressure-test-guide.md)
- [Docker部署指南](docs/docker-deployment-fix-guide.md)

## 👥 开发团队

| 角色 | 姓名 | 职责 |
|-----|------|------|
| 产品经理 | 张三 | 需求确认 |
| 技术负责人 | 李四 | 技术实现 |
| 测试经理 | 王五 | 质量保证 |

---

*本文档最后更新时间：2025-08-25*
