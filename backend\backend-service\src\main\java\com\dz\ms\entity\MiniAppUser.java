package com.dz.ms.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 小程序用户实体
 * <AUTHOR>
 * @date 2025-01-27
 * @version 1.0.0
 */
@Data
@TableName("users")
public class MiniAppUser implements Serializable {
    /** 主键ID */
    @TableId
    private Long id;

    /** 手机号 */
    @TableField("phone")
    private String phone;

    /** 密码 */
    @TableField("password")
    private String password;

    /** 昵称 */
    @TableField("nickname")
    private String nickname;

    /** 头像 */
    @TableField("avatar")
    private String avatar;

    /** 创建人 */
    @TableField("created_by")
    private Long createdBy;

    /** 修改人 */
    @TableField("updated_by")
    private Long updatedBy;

    /** 创建时间 */
    @TableField("created_at")
    private Date createdAt;

    /** 更新时间 */
    @TableField("updated_at")
    private Date updatedAt;
} 