apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql5744-deploy
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql5744
  template:
    metadata:
      labels:
        app: mysql5744
    spec:
      containers:
      - name: mysql5744
        image: mysql:5.7.44
        ports:
        - containerPort: 3306
        # MySQL环境变量配置（必填）
        env:
        - name: MYSQL_ROOT_PASSWORD
          value: "123456"  # root用户密码
        - name: MYSQL_DATABASE
          value: "nacos"   # 初始化数据库（可选）
        - name: MYSQL_CHARACTER_SET_SERVER
          value: "utf8mb4" # 字符集配置（可选）
        - name: MYSQL_COLLATION_SERVER
          value: "utf8mb4_general_ci"
        # 数据存储挂载（合并部分）
        volumeMounts:
        - name: mysql-data
          mountPath: /var/lib/mysql  # MySQL数据存储路径（容器内）
      # 存储卷配置（合并部分）
      volumes:
      - name: mysql-data
        emptyDir: {}  # 临时存储（仅测试用，Pod删除后数据会丢失）
---

apiVersion: v1
kind: Service
metadata:
  name: mysql5744-service
spec:
  selector:
    app: mysql5744
  ports:
  - port: 3306
    targetPort: 3306
    nodePort: 30306
  type: NodePort