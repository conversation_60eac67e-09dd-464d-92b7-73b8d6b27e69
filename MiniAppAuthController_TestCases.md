# 小程序认证控制器(MiniAppAuthController)测试用例

## 1. 用户注册接口(/auth1/register)测试用例

### 测试用例1.1：正常注册
- **用例描述**：使用有效的手机号、密码、确认密码、验证码进行注册
- **前置条件**：手机号未被注册，验证码有效
- **请求方法**：POST
- **请求URL**：/auth1/register
- **请求头**：Content-Type: application/json
- **请求体**：
```json
{
  "phone": "13800138000",
  "password": "123456",
  "confirmPassword": "123456",
  "verificationCode": "123456",
  "nickname": "测试用户",
  "avatar": "https://example.com/avatar.jpg"
}
```
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "注册成功"
}
```
- **后置处理**：验证数据库中是否成功创建了用户记录

### 测试用例1.2：密码不一致
- **用例描述**：密码与确认密码不一致
- **前置条件**：无
- **请求方法**：POST
- **请求URL**：/auth1/register
- **请求头**：Content-Type: application/json
- **请求体**：
```json
{
  "phone": "13800138001",
  "password": "123456",
  "confirmPassword": "654321",
  "verificationCode": "123456",
  "nickname": "测试用户",
  "avatar": "https://example.com/avatar.jpg"
}
```
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 500,
  "message": "两次输入的密码不一致",
  "data": null
}
```

### 测试用例1.3：密码长度不足
- **用例描述**：密码长度少于6位
- **前置条件**：无
- **请求方法**：POST
- **请求URL**：/auth1/register
- **请求头**：Content-Type: application/json
- **请求体**：
```json
{
  "phone": "13800138002",
  "password": "12345",
  "confirmPassword": "12345",
  "verificationCode": "123456",
  "nickname": "测试用户",
  "avatar": "https://example.com/avatar.jpg"
}
```
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 500,
  "message": "密码长度不能少于6位",
  "data": null
}
```

### 测试用例1.4：手机号格式错误
- **用例描述**：手机号格式不符合规范
- **前置条件**：无
- **请求方法**：POST
- **请求URL**：/auth1/register
- **请求头**：Content-Type: application/json
- **请求体**：
```json
{
  "phone": "1380013",
  "password": "123456",
  "confirmPassword": "123456",
  "verificationCode": "123456",
  "nickname": "测试用户",
  "avatar": "https://example.com/avatar.jpg"
}
```
- **预期结果**：
  - 状态码：400
  - 响应体包含手机号格式错误的提示信息

### 测试用例1.5：手机号已注册
- **用例描述**：使用已注册的手机号进行注册
- **前置条件**：手机号已被注册
- **请求方法**：POST
- **请求URL**：/auth1/register
- **请求头**：Content-Type: application/json
- **请求体**：
```json
{
  "phone": "13800138000",
  "password": "123456",
  "confirmPassword": "123456",
  "verificationCode": "123456",
  "nickname": "测试用户",
  "avatar": "https://example.com/avatar.jpg"
}
```
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 500,
  "message": "注册失败，请检查验证码或手机号是否已被注册",
  "data": null
}
```

### 测试用例1.6：验证码错误
- **用例描述**：使用错误的验证码进行注册
- **前置条件**：手机号未被注册，验证码无效
- **请求方法**：POST
- **请求URL**：/auth1/register
- **请求头**：Content-Type: application/json
- **请求体**：
```json
{
  "phone": "13800138003",
  "password": "123456",
  "confirmPassword": "123456",
  "verificationCode": "000000",
  "nickname": "测试用户",
  "avatar": "https://example.com/avatar.jpg"
}
```
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 500,
  "message": "注册失败，请检查验证码或手机号是否已被注册",
  "data": null
}
```

## 2. 用户登录接口(/auth1/login)测试用例

### 测试用例2.1：正常登录
- **用例描述**：使用正确的手机号和密码登录
- **前置条件**：用户已注册
- **请求方法**：POST
- **请求URL**：/auth1/login
- **请求头**：Content-Type: application/json
- **请求体**：
```json
{
  "phone": "13800138000",
  "password": "123456"
}
```
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMzgwMDEzODAwMCIsImlhdCI6MTYyNTAyMTQ1MCwiZXhwIjoxNjI1MDI1MDUwfQ.X8bGjuVUNcmZ9mW1hFpNQ9xJ6T9X3QzZmLf-Iy4v0Sw"
}
```
- **后置处理**：验证返回的token是否有效

### 测试用例2.2：手机号不存在
- **用例描述**：使用未注册的手机号登录
- **前置条件**：手机号未注册
- **请求方法**：POST
- **请求URL**：/auth1/login
- **请求头**：Content-Type: application/json
- **请求体**：
```json
{
  "phone": "13900139000",
  "password": "123456"
}
```
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 500,
  "message": "手机号或密码错误",
  "data": null
}
```

### 测试用例2.3：密码错误
- **用例描述**：使用正确的手机号但错误的密码登录
- **前置条件**：用户已注册
- **请求方法**：POST
- **请求URL**：/auth1/login
- **请求头**：Content-Type: application/json
- **请求体**：
```json
{
  "phone": "13800138000",
  "password": "wrongpassword"
}
```
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 500,
  "message": "手机号或密码错误",
  "data": null
}
```

### 测试用例2.4：手机号格式错误
- **用例描述**：使用格式错误的手机号登录
- **前置条件**：无
- **请求方法**：POST
- **请求URL**：/auth1/login
- **请求头**：Content-Type: application/json
- **请求体**：
```json
{
  "phone": "1380013",
  "password": "123456"
}
```
- **预期结果**：
  - 状态码：400
  - 响应体包含手机号格式错误的提示信息

## 3. 发送验证码接口(/auth1/sendCode)测试用例

### 测试用例3.1：正常发送验证码
- **用例描述**：向有效手机号发送验证码
- **前置条件**：无
- **请求方法**：POST
- **请求URL**：/auth1/sendCode
- **请求头**：Content-Type: application/json
- **请求体**：
```json
{
  "phone": "13800138000",
  "verificationCode": "123456"
}
```
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "验证码发送成功"
}
```
- **后置处理**：验证验证码是否已发送到指定手机号

### 测试用例3.2：手机号格式错误
- **用例描述**：向格式错误的手机号发送验证码
- **前置条件**：无
- **请求方法**：POST
- **请求URL**：/auth1/sendCode
- **请求头**：Content-Type: application/json
- **请求体**：
```json
{
  "phone": "1380013",
  "verificationCode": "123456"
}
```
- **预期结果**：
  - 状态码：400
  - 响应体包含手机号格式错误的提示信息

### 测试用例3.3：发送验证码失败
- **用例描述**：由于系统原因导致验证码发送失败
- **前置条件**：系统短信服务不可用
- **请求方法**：POST
- **请求URL**：/auth1/sendCode
- **请求头**：Content-Type: application/json
- **请求体**：
```json
{
  "phone": "13800138000",
  "verificationCode": "123456"
}
```
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 500,
  "message": "验证码发送失败，请稍后重试",
  "data": null
}
```

## 4. 验证token接口(/auth1/validateToken)测试用例

### 测试用例4.1：有效token验证
- **用例描述**：验证有效的token
- **前置条件**：用户已登录并获取有效token
- **请求方法**：POST
- **请求URL**：/auth1/validateToken
- **请求头**：Authorization: Bearer {token}
- **请求体**：无
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": true
}
```

### 测试用例4.2：无效token验证
- **用例描述**：验证无效的token
- **前置条件**：token已过期或不合法
- **请求方法**：POST
- **请求URL**：/auth1/validateToken
- **请求头**：Authorization: Bearer {invalid_token}
- **请求体**：无
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": false
}
```

### 测试用例4.3：token格式错误
- **用例描述**：验证格式错误的token
- **前置条件**：无
- **请求方法**：POST
- **请求URL**：/auth1/validateToken
- **请求头**：Authorization: {token}（缺少Bearer前缀）
- **请求体**：无
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 500,
  "message": "Token格式错误",
  "data": null
}
```

### 测试用例4.4：缺少token
- **用例描述**：请求头中缺少Authorization
- **前置条件**：无
- **请求方法**：POST
- **请求URL**：/auth1/validateToken
- **请求头**：无Authorization头
- **请求体**：无
- **预期结果**：
  - 状态码：400或500
  - 响应体包含缺少token的错误信息

## 5. 获取用户信息接口(/auth1/userInfo)测试用例

### 测试用例5.1：正常获取用户信息
- **用例描述**：使用有效token获取用户信息
- **前置条件**：用户已登录并获取有效token
- **请求方法**：GET
- **请求URL**：/auth1/userInfo
- **请求头**：Authorization: Bearer {token}
- **请求体**：无
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "nickname": "测试用户",
    "avatar": "https://example.com/avatar.jpg",
    "createTime": "2025-01-27T12:00:00"
  }
}
```

### 测试用例5.2：无效token
- **用例描述**：使用无效token获取用户信息
- **前置条件**：token已过期或不合法
- **请求方法**：GET
- **请求URL**：/auth1/userInfo
- **请求头**：Authorization: Bearer {invalid_token}
- **请求体**：无
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 500,
  "message": "Token无效",
  "data": null
}
```

### 测试用例5.3：用户不存在
- **用例描述**：token有效但对应的用户已被删除
- **前置条件**：用户已登录获取token后被删除
- **请求方法**：GET
- **请求URL**：/auth1/userInfo
- **请求头**：Authorization: Bearer {token}
- **请求体**：无
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 500,
  "message": "用户不存在",
  "data": null
}
```

### 测试用例5.4：token格式错误
- **用例描述**：验证格式错误的token
- **前置条件**：无
- **请求方法**：GET
- **请求URL**：/auth1/userInfo
- **请求头**：Authorization: {token}（缺少Bearer前缀）
- **请求体**：无
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 500,
  "message": "Token格式错误",
  "data": null
}
```

## 6. 更新用户信息接口(/auth1/userInfo)测试用例

### 测试用例6.1：正常更新用户信息
- **用例描述**：使用有效token更新用户信息
- **前置条件**：用户已登录并获取有效token
- **请求方法**：PUT
- **请求URL**：/auth1/userInfo
- **请求头**：
  - Content-Type: application/json
  - Authorization: Bearer {token}
- **请求体**：
```json
{
  "nickname": "新昵称",
  "avatar": "https://example.com/new-avatar.jpg"
}
```
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "更新成功"
}
```
- **后置处理**：验证数据库中用户信息是否已更新

### 测试用例6.2：无效token
- **用例描述**：使用无效token更新用户信息
- **前置条件**：token已过期或不合法
- **请求方法**：PUT
- **请求URL**：/auth1/userInfo
- **请求头**：
  - Content-Type: application/json
  - Authorization: Bearer {invalid_token}
- **请求体**：
```json
{
  "nickname": "新昵称",
  "avatar": "https://example.com/new-avatar.jpg"
}
```
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 500,
  "message": "Token无效",
  "data": null
}
```

### 测试用例6.3：用户不存在
- **用例描述**：token有效但对应的用户已被删除
- **前置条件**：用户已登录获取token后被删除
- **请求方法**：PUT
- **请求URL**：/auth1/userInfo
- **请求头**：
  - Content-Type: application/json
  - Authorization: Bearer {token}
- **请求体**：
```json
{
  "nickname": "新昵称",
  "avatar": "https://example.com/new-avatar.jpg"
}
```
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 500,
  "message": "用户不存在",
  "data": null
}
```

### 测试用例6.4：更新失败
- **用例描述**：由于系统原因导致更新失败
- **前置条件**：用户已登录并获取有效token，但系统数据库服务异常
- **请求方法**：PUT
- **请求URL**：/auth1/userInfo
- **请求头**：
  - Content-Type: application/json
  - Authorization: Bearer {token}
- **请求体**：
```json
{
  "nickname": "新昵称",
  "avatar": "https://example.com/new-avatar.jpg"
}
```
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 500,
  "message": "更新失败",
  "data": null
}
```

### 测试用例6.5：token格式错误
- **用例描述**：验证格式错误的token
- **前置条件**：无
- **请求方法**：PUT
- **请求URL**：/auth1/userInfo
- **请求头**：
  - Content-Type: application/json
  - Authorization: {token}（缺少Bearer前缀）
- **请求体**：
```json
{
  "nickname": "新昵称",
  "avatar": "https://example.com/new-avatar.jpg"
}
```
- **预期结果**：
  - 状态码：200
  - 响应体：
```json
{
  "code": 500,
  "message": "Token格式错误",
  "data": null
}
```