package com.dz.ms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.ms.entity.MiniAppUser;
import com.dz.ms.mapper.MiniAppUserMapper;
import com.dz.ms.service.MiniAppUserService;
import com.dz.ms.util.JwtUtil;
import com.dz.ms.vo.MiniAppUserVO;

import lombok.extern.slf4j.Slf4j;

import org.hibernate.validator.internal.util.logging.Log_.logger;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 小程序用户Service实现类
 * <AUTHOR>
 * @date 2025-01-27
 * @version 1.0.0
 */
@Service
@Slf4j
public class MiniAppUserServiceImpl extends ServiceImpl<MiniAppUserMapper, MiniAppUser> implements MiniAppUserService {

    // 存储验证码的Map，key为手机号，value为验证码和过期时间
    private static final Map<String, VerificationCodeInfo> VERIFICATION_CODE_MAP = new ConcurrentHashMap<>();
    // 验证码有效期（毫秒）
    private static final long VERIFICATION_CODE_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟

    private final PasswordEncoder passwordEncoder;

    public MiniAppUserServiceImpl(PasswordEncoder passwordEncoder) {
        this.passwordEncoder = passwordEncoder;
    }

    @Override
    public boolean register(String phone, String password, String verificationCode, String nickname, String avatar) {
        // 验证验证码
        if (!validateVerificationCode(phone, verificationCode)) {
            log.error("Invalid verification code.");
            return false;
        }

        // 检查手机号是否已存在
        if (getByPhone(phone) != null) {
            log.error("User already exists.");
            return false;
        }

        // 创建新用户
        MiniAppUser user = new MiniAppUser();
        user.setPhone(phone);
        user.setPassword(passwordEncoder.encode(password));
        user.setNickname(nickname != null ? nickname : "用户" + phone.substring(7));
        user.setAvatar(avatar);
        user.setCreatedAt(new Date());
        user.setUpdatedAt(new Date());

        // 保存用户
        boolean saved = save(user);
        if (saved) {
            // 注册成功后清除验证码
            VERIFICATION_CODE_MAP.remove(phone);
        }
        return saved;
    }

    @Override
    public String login(String phone, String password) {
        // 根据手机号查询用户
        MiniAppUser user = getByPhone(phone);
        if (user == null) {
            return null;
        }

        // 验证密码
        if (!passwordEncoder.matches(password, user.getPassword())) {
            return null;
        }

        // 生成JWT token
        return JwtUtil.generateToken(user.getPhone());
    }

    @Override
    public boolean sendVerificationCode(String phone) {
        // 生成6位随机验证码
        String code = generateRandomCode();
        
        // 存储验证码和过期时间
        VerificationCodeInfo codeInfo = new VerificationCodeInfo();
        codeInfo.setCode(code);
        codeInfo.setExpireTime(System.currentTimeMillis() + VERIFICATION_CODE_EXPIRE_TIME);
        VERIFICATION_CODE_MAP.put(phone, codeInfo);

        // TODO: 这里应该调用短信服务发送验证码
        // 目前先打印到控制台，实际项目中需要集成短信服务
        System.out.println("发送验证码到手机号: " + phone + ", 验证码: " + code);

        // 启动一个线程，在验证码过期后从Map中移除
        new Thread(() -> {
            try {
                Thread.sleep(VERIFICATION_CODE_EXPIRE_TIME);
                VERIFICATION_CODE_MAP.remove(phone);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();

        return true;
    }

    @Override
    public boolean validateToken(String token) {
        try {
            String phone = JwtUtil.extractUsername(token);
            if (phone == null) {
                return false;
            }
            
            // 检查用户是否存在
            MiniAppUser user = getByPhone(phone);
            return user != null;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public MiniAppUser getByPhone(String phone) {
        LambdaQueryWrapper<MiniAppUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MiniAppUser::getPhone, phone);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public boolean updateUserInfo(Long userId, String nickname, String avatar) {
        UpdateWrapper<MiniAppUser> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", userId);
        
        if (nickname != null && !nickname.isEmpty()) {
            updateWrapper.set("nickname", nickname);
        }
        if (avatar != null && !avatar.isEmpty()) {
            updateWrapper.set("avatar", avatar);
        }
        updateWrapper.set("updated_at", new Date());
        
        return update(updateWrapper);
    }

    @Override
    public MiniAppUserVO getUserInfo(Long userId) {
        MiniAppUser user = getById(userId);
        if (user == null) {
            return null;
        }
        
        MiniAppUserVO vo = new MiniAppUserVO();
        BeanUtils.copyProperties(user, vo);
        return vo;
    }

    /**
     * 验证验证码
     */
    private boolean validateVerificationCode(String phone, String code) {
        VerificationCodeInfo codeInfo = VERIFICATION_CODE_MAP.get(phone);
        if (codeInfo == null) {
            return false;
        }
        
        // 检查是否过期
        if (System.currentTimeMillis() > codeInfo.getExpireTime()) {
            VERIFICATION_CODE_MAP.remove(phone);
            return false;
        }
        
        // 检查验证码是否匹配
        return code.equals(codeInfo.getCode());
    }

    /**
     * 生成6位随机验证码
     */
    private String generateRandomCode() {
        return String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
    }

    /**
     * 验证码信息内部类
     */
    private static class VerificationCodeInfo {
        private String code;
        private long expireTime;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public long getExpireTime() {
            return expireTime;
        }

        public void setExpireTime(long expireTime) {
            this.expireTime = expireTime;
        }
    }
} 