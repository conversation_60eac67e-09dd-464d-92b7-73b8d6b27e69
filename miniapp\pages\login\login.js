// pages/login/login.js
const { request } = require('../../utils/request');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isRegister: false,  // 是否为注册模式
    phone: '',          // 手机号
    password: '',       // 密码
    confirmPassword: '', // 确认密码
    verificationCode: '', // 验证码
    counting: false,    // 是否在倒计时
    countDown: 60,      // 倒计时秒数
    agreeProtocol: false, // 是否同意协议
    isIOS: false        // 是否为iOS设备
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 检测是否为iOS设备
    wx.getSystemInfo({
      success: (res) => {
        this.setData({
          isIOS: res.platform === 'ios'
        });
      }
    });
  },

  /**
   * 输入手机号
   */
  inputPhone: function(e) {
    this.setData({
      phone: e.detail.value
    });
  },

  /**
   * 输入密码
   */
  inputPassword: function(e) {
    this.setData({
      password: e.detail.value
    });
  },

  /**
   * 输入确认密码
   */
  inputConfirmPassword: function(e) {
    this.setData({
      confirmPassword: e.detail.value
    });
  },

  /**
   * 输入验证码
   */
  inputVerificationCode: function(e) {
    this.setData({
      verificationCode: e.detail.value
    });
  },

  /**
   * 切换登录/注册模式
   */
  switchLoginRegister: function() {
    this.setData({
      isRegister: !this.data.isRegister,
      // 切换模式时清空输入
      password: '',
      confirmPassword: '',
      verificationCode: ''
    });
  },

  /**
   * 获取验证码
   */
  getVerificationCode: function() {
    // 验证手机号
    if (!this.data.phone || this.data.phone.length !== 11) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }
    request({
      url: '/auth1/sendCode',
      method: 'POST',
      data: { phone: this.data.phone }
    }).then(res => {
      wx.showToast({ title: res.msg, icon: res.success ? 'success' : 'none' });
      if (res.success) this.startCountDown();
    }).catch(() => {
      wx.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
    });
  },

  /**
   * 开始倒计时
   */
  startCountDown: function() {
    this.setData({
      counting: true,
      countDown: 60
    });

    const timer = setInterval(() => {
      if (this.data.countDown <= 1) {
        clearInterval(timer);
        this.setData({
          counting: false
        });
      } else {
        this.setData({
          countDown: this.data.countDown - 1
        });
      }
    }, 1000);
  },

  /**
   * 切换协议同意状态
   */
  toggleAgreement: function() {
    this.setData({
      agreeProtocol: !this.data.agreeProtocol
    });
  },

  /**
   * 显示用户协议
   */
  showUserAgreement: function() {
    // TODO: 跳转到用户协议页面
    wx.navigateTo({
      url: '/pages/agreement/user'
    });
  },

  /**
   * 显示隐私政策
   */
  showPrivacyPolicy: function() {
    // TODO: 跳转到隐私政策页面
    wx.navigateTo({
      url: '/pages/agreement/privacy'
    });
  },

  /**
   * 登录
   */
  login: function() {
    // 表单验证
    if (!this.data.phone || this.data.phone.length !== 11) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }

    if (!this.data.password) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }

    if (!this.data.agreeProtocol) {
      wx.showToast({
        title: '请阅读并同意用户协议和隐私政策',
        icon: 'none'
      });
      return;
    }
    wx.showLoading({ title: '登录中...' });
    request({
      url: '/auth1/login',
      method: 'POST',
      data: { phone: this.data.phone, password: this.data.password }
    }).then(res => {
      wx.hideLoading();
      if (res.success) {
        wx.setStorageSync('token', res.data);
        wx.showToast({ title: '登录成功', icon: 'success', duration: 1500 });
        setTimeout(() => {
          wx.switchTab({ url: '/pages/index/index' });
        }, 1500);
      } else {
        wx.showToast({ title: res.msg || '登录失败', icon: 'none' });
      }
    }).catch(() => {
      wx.hideLoading();
      wx.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
    });
  },

  /**
   * 注册
   */
  register: function() {
    // 表单验证
    if (!this.data.phone || this.data.phone.length !== 11) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }

    if (!this.data.password) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }

    if (this.data.password !== this.data.confirmPassword) {
      wx.showToast({
        title: '两次输入的密码不一致',
        icon: 'none'
      });
      return;
    }

    if (!this.data.verificationCode) {
      wx.showToast({
        title: '请输入验证码',
        icon: 'none'
      });
      return;
    }

    if (!this.data.agreeProtocol) {
      wx.showToast({
        title: '请阅读并同意用户协议和隐私政策',
        icon: 'none'
      });
      return;
    }
    wx.showLoading({ title: '注册中...' });
    request({
      url: '/auth1/register',
      method: 'POST',
      data: {
        phone: this.data.phone,
        password: this.data.password,
        confirmPassword: this.data.confirmPassword,
        verificationCode: this.data.verificationCode
      }
    }).then(res => {
      wx.hideLoading();
      wx.showToast({ title: res.msg, icon: res.success ? 'success' : 'none', duration: 1500 });
      if (res.success) {
        setTimeout(() => {
          this.setData({
            isRegister: false,
            password: '',
            confirmPassword: '',
            verificationCode: ''
          });
        }, 1500);
      }
    }).catch(() => {
      wx.hideLoading();
      wx.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
    });
  },

  /**
   * 微信登录
   */
  wechatLogin: function() {
    // TODO: 调用微信登录接口
    wx.login({
      success: (res) => {
        if (res.code) {
          // 获取到微信登录code后，发送到后端换取用户信息
          // wx.request({
          //   url: 'API_URL/wechatLogin',
          //   method: 'POST',
          //   data: {
          //     code: res.code
          //   },
          //   success: (res) => {
          //     if (res.data.success) {
          //       // 保存登录状态和token
          //       wx.setStorageSync('token', res.data.token);
          //       wx.setStorageSync('userInfo', res.data.userInfo);
          //       
          //       // 跳转到首页
          //       wx.switchTab({
          //         url: '/pages/index/index'
          //       });
          //     } else {
          //       wx.showToast({
          //         title: res.data.message || '微信登录失败',
          //         icon: 'none'
          //       });
          //     }
          //   },
          //   fail: () => {
          //     wx.showToast({
          //       title: '网络错误，请稍后重试',
          //       icon: 'none'
          //     });
          //   }
          // });
          
          // 模拟登录成功
          wx.showToast({
            title: '微信登录成功',
            icon: 'success',
            duration: 1500,
            success: () => {
              setTimeout(() => {
                wx.switchTab({
                  url: '/pages/index/index'
                });
              }, 1500);
            }
          });
        } else {
          wx.showToast({
            title: '微信登录失败',
            icon: 'none'
          });
        }
      }
    });
  },

  /**
   * Apple登录 (仅iOS设备)
   */
  appleLogin: function() {
    // TODO: 调用Apple登录接口
    // 由于微信小程序暂不支持直接调用Apple登录，这里仅作为示例
    wx.showToast({
      title: 'Apple登录功能开发中',
      icon: 'none'
    });
  }
})