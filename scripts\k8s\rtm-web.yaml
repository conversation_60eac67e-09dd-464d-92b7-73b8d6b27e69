apiVersion: apps/v1
kind: Deployment
metadata:
  name: rtm-web-deploy
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rtm-web
  template:
    metadata:
      labels:
        app: rtm-web
    spec:
      containers:
      - name: rtm-web
        image: rtm-web:latest
        imagePullPolicy: Never  # 关键配置：不拉取远程镜像,从本地镜像获取
        ports:
        - containerPort: 80
    
---

apiVersion: v1
kind: Service
metadata:
  name: rtm-web-service  # Service 名称
spec:
  selector:
    app: rtm-web  # 与 Deployment 中 Pod 的标签匹配（关键！）
  ports:
  - port: 8080        # Service 自身的端口（集群内部访问用）
    targetPort: 80    # 映射到 Pod 中容器的端口（需与 containerPort 一致）
    nodePort: 30080   # 节点暴露的端口（范围：30000-32767，外部访问用）
  type: NodePort      # 服务类型：在每个节点上开放一个端口，供外部访问