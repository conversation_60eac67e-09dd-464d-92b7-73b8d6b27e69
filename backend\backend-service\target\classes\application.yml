server:
  port: 9091

spring:
  application:
    name: rtm-backend
  config:
    import: "optional:nacos:rtm-backend.yaml"
  cloud:
    nacos:
      server-addr: nacos-service:8848 
      discovery:
        # 临时关闭服务注册
        register-enabled: false  
  datasource:
    url: ****************************************************************************************************  
    username: root  
    password: 781111  
    driver-class-name: com.mysql.cj.jdbc.Driver  
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss  
    time-zone: Asia/Shanghai  
  sql:
    init:
      mode: always  
      schema-locations: classpath:schema.sql  
mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml  
  type-aliases-package: com.dz.ms.entity  
  global-config:
    db-config:
      id-type: auto  
      logic-delete-value: 1  
      logic-not-delete-value: 0  
logging:
  level:
    com.dz.ms: DEBUG  # 补充完整的日志级别配置（根据需求调整）

# spring:
#   application:
#     name: rtm-backend
#   config:
#     import: "optional:nacos:rtm-backend.yaml"
#   cloud:
#     nacos:
#       server-addr: nacos-service:8848 
#放到nacos中了（要注意Nacos中不能有注释，要去掉注释复制过去）
#    sentinel:
#      transport:
#        # sentinel控制台地址
#        dashboard: localhost:8080
#        # 指定应用与Sentinel控制台交互的端口，应用本地会起一个该端口占用的HttpServer
#        port: 8719

#      datasource:
#        # 流控规则的数据源配置
#        flow:
#          nacos:
#            server-addr: localhost:8848
#            data-id: ${spring.application.name}-flow-rules
#            group-id: DEFAULT_GROUP
#            data-type: json
#            rule-type: flow
#        # 熔断降级规则
#        degradeRule:
#          nacos:
#            server-addr: ${spring.cloud.nacos.discovery.server-addr}  # 复用Nacos地址
#            data-id: sentinel-degrade-config.json  # Nacos中配置文件的Data ID（自定义）
#            group-id: DEFAULT_GROUP  # Nacos分组（默认）
#            data-type: json  # 配置文件格式（必须为JSON）
#            rule-type: degrade  # 指定为熔断降级规则（关键）
#
#      http-method-specify: true #开启请求方式前缀
#      eager: true  # 启用饥饿加载模式，服务启动时就会自动注册到 Dashboard


  # datasource:
  #   url: jdbc:mysql://************:30306/rtm?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai
  #   username: root
  #   password: 123456
  #   driver-class-name: com.mysql.cj.jdbc.Driver
  # jackson:
  #   date-format: yyyy-MM-dd HH:mm:ss
  #   time-zone: Asia/Shanghai
  # sql:
  #   init:
  #     mode: always
  #     schema-locations: classpath:schema.sql


  # mybatis-plus:
  # mapper-locations: classpath:mapper/**/*.xml
  # type-aliases-package: com.dz.ms.entity
  # global-config:
  #   db-config:
  #     id-type: auto
  #     logic-delete-value: 1
  #     logic-not-delete-value: 0

  # logging:
  # level:
  #   com.dz.ms: DEBUG

  # swagger:
  # enabled: true