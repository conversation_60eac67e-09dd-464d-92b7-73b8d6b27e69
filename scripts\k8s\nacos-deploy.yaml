apiVersion: apps/v1
kind: Deployment
metadata:
  name: nacos-deploy
spec:
  replicas: 1  # 单机模式建议先使用1个副本（多副本需集群模式）
  selector:
    matchLabels:
      app: nacos
  template:
    metadata:
      labels:
        app: nacos
    spec:
      containers:
      - name: nacos
        image: nacos/nacos-server:v2.0.3
        ports:
        - containerPort: 8848
        env:
        # 1. 强制单机模式,否则nacos启动会报错（看启动日志）
        - name: MODE
          value: "standalone"
        # 2. 指定嵌入式数据库（单机模式默认，但需显式配置避免歧义）
        - name: SPRING_DATASOURCE_PLATFORM
          value: "embedded"  # 关键：使用嵌入式Derby数据库
        # 3. 关闭认证（避免因未配置用户密码导致的额外错误）
        - name: NACOS_AUTH_ENABLE
          value: "false"
        # 4. 可选：指定数据存储路径（容器内默认路径有写权限）
        - name: NACOS_DATA_DIR
          value: "/home/<USER>/data"
        - name: NACOS_LOG_DIR
          value: "/home/<USER>/logs"
---

apiVersion: v1
kind: Service
metadata:
  name: nacos-service
spec:
  selector:
    app: nacos
  ports:
  - port: 8848
    targetPort: 8848
    nodePort: 30848
  type: NodePort
