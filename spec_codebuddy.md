# AWX项目介绍文档

## 项目概述

AWX是一个基于微服务架构的企业级后台管理系统，集成了小程序、Web前端和后端服务。项目采用Spring Boot + Vue3 + 微信小程序的技术栈，提供用户管理、权限控制、数据可视化、低代码平台等功能。

### 基本信息
- **项目名称**: AWX系统
- **版本**: 1.0.0
- **开发团队**: 腾讯云团队
- **预计上线**: 2025-10-01
- **估算工期**: 30人天

## 功能清单

### 核心功能模块

| 模块分类 | 功能名称 | 功能描述 | 实现状态 |
|---------|---------|---------|---------|
| **用户管理** | 用户注册 | 支持手机号注册，短信验证码验证 | ✅ |
| | 用户登录 | JWT Token认证，支持小程序和Web登录 | ✅ |
| | 用户信息管理 | 个人资料修改、头像上传 | ✅ |
| | 密码管理 | 密码修改、找回密码 | ✅ |
| **权限管理** | 角色管理 | 角色创建、编辑、删除、权限分配 | ✅ |
| | 菜单管理 | 动态菜单配置、权限控制 | ✅ |
| | 用户角色关联 | 用户角色绑定、权限继承 | ✅ |
| **内容管理** | 产品管理 | 产品信息展示、特性管理 | ✅ |
| | 解决方案 | 方案展示、标签分类 | ✅ |
| | 新闻资讯 | 新闻发布、分类管理、浏览统计 | ✅ |
| | 轮播图管理 | 首页Banner管理 | ✅ |
| **低代码平台** | 元数据管理 | 实体定义、字段配置 | ✅ |
| | 代码生成 | 自动生成CRUD代码 | ✅ |
| | 动态表单 | 表单配置、数据录入 | ✅ |
| **系统管理** | 会员管理 | 会员信息、审核状态 | ✅ |
| | 反馈管理 | 用户反馈收集、处理 | ✅ |
| | 系统监控 | 服务状态、性能监控 | ✅ |

### 小程序功能

| 页面 | 功能描述 |
|-----|---------|
| 首页 | 产品展示、新闻资讯、轮播图 |
| 产品 | 产品列表、详情展示 |
| 解决方案 | 方案分类、详情查看 |
| 新闻 | 资讯列表、内容阅读 |
| 我的 | 个人中心、账户管理、帮助反馈 |

## 项目架构

### 系统架构图

```mermaid
graph TB
    subgraph "前端层"
        A[微信小程序] 
        B[Vue3 Web管理端]
    end
    
    subgraph "网关层"
        C[Spring Cloud Gateway]
    end
    
    subgraph "服务层"
        D[用户服务]
        E[权限服务] 
        F[内容服务]
        G[低代码服务]
    end
    
    subgraph "数据层"
        H[MySQL数据库]
        I[Redis缓存]
    end
    
    subgraph "基础设施"
        J[Nacos注册中心]
        K[Sentinel流量控制]
    end
    
    A --> C
    B --> C
    C --> D
    C --> E
    C --> F
    C --> G
    D --> H
    E --> H
    F --> H
    G --> H
    D --> I
    E --> I
    C --> J
    D --> J
    E --> J
    F --> J
    G --> J
    C --> K
    D --> K
    E --> K
    F --> K
    G --> K
```

### 技术架构

#### 后端技术栈
- **框架**: Spring Boot 2.7.18
- **微服务**: Spring Cloud 2021.0.8
- **服务注册**: Nacos 2021.0.5.0
- **API网关**: Spring Cloud Gateway
- **流量控制**: Alibaba Sentinel
- **数据库**: MySQL 8.0.33
- **ORM框架**: MyBatis Plus *******
- **安全框架**: Spring Security + JWT
- **API文档**: SpringDoc OpenAPI 1.6.15
- **模板引擎**: FreeMarker 2.3.31
- **JSON处理**: FastJSON 1.2.83

#### 前端技术栈
- **Web框架**: Vue 3.5.13
- **构建工具**: Vite 6.3.5
- **UI组件**: Element Plus 2.9.11
- **状态管理**: Pinia 3.0.2
- **路由管理**: Vue Router 4.5.1
- **HTTP客户端**: Axios 1.9.0
- **开发语言**: TypeScript 5.8.3
- **样式预处理**: Sass 1.89.1

#### 小程序技术栈
- **框架**: 微信小程序原生框架
- **开发语言**: JavaScript
- **样式**: WXSS
- **模板**: WXML

## 目录结构

```
awx/
├── backend/                    # 后端服务
│   ├── pom.xml                # 父级Maven配置
│   ├── gateway/               # API网关服务
│   │   ├── src/main/java/     # 网关源码
│   │   └── pom.xml           # 网关依赖配置
│   ├── backend-service/       # 核心业务服务
│   │   ├── src/main/java/     # 业务源码
│   │   │   └── com/dz/ms/     # 主包路径
│   │   │       ├── controller/    # 控制器层
│   │   │       ├── service/       # 服务层
│   │   │       ├── mapper/        # 数据访问层
│   │   │       ├── entity/        # 实体类
│   │   │       ├── dto/           # 数据传输对象
│   │   │       ├── vo/            # 视图对象
│   │   │       ├── config/        # 配置类
│   │   │       ├── filter/        # 过滤器
│   │   │       ├── util/          # 工具类
│   │   │       └── lowcode/       # 低代码模块
│   │   ├── src/main/resources/    # 配置文件
│   │   └── pom.xml               # 服务依赖配置
│   ├── common/                # 公共模块
│   │   └── common-core/       # 核心公共组件
│   └── sql/                   # 数据库脚本
├── frontend/                  # Web前端
│   ├── src/                   # 源码目录
│   │   ├── views/             # 页面组件
│   │   │   ├── Auth/          # 认证相关页面
│   │   │   ├── User/          # 用户管理
│   │   │   ├── Role/          # 角色管理
│   │   │   ├── Menu/          # 菜单管理
│   │   │   ├── Member/        # 会员管理
│   │   │   ├── LowCode/       # 低代码平台
│   │   │   └── Welcome/       # 欢迎页
│   │   ├── components/        # 公共组件
│   │   ├── layouts/           # 布局组件
│   │   ├── router/            # 路由配置
│   │   ├── store/             # 状态管理
│   │   ├── api/               # API接口
│   │   └── utils/             # 工具函数
│   ├── public/                # 静态资源
│   └── package.json           # 依赖配置
├── miniapp/                   # 微信小程序
│   ├── pages/                 # 页面目录
│   │   ├── index/             # 首页
│   │   ├── login/             # 登录页
│   │   ├── product/           # 产品页
│   │   ├── solution/          # 解决方案页
│   │   ├── news/              # 新闻页
│   │   └── profile/           # 个人中心
│   │       ├── account/       # 账户管理
│   │       ├── security/      # 安全设置
│   │       ├── help/          # 帮助中心
│   │       ├── feedback/      # 意见反馈
│   │       └── about/         # 关于我们
│   ├── utils/                 # 工具函数
│   ├── images/                # 图片资源
│   ├── app.js                 # 小程序入口
│   ├── app.json               # 小程序配置
│   └── app.wxss               # 全局样式
├── docs/                      # 项目文档
├── scripts/                   # 部署脚本
│   ├── k8s/                   # Kubernetes配置
│   └── start-sentinel-dashboard.bat  # Sentinel启动脚本
├── nacos-configs/             # Nacos配置文件
└── database_schema.sql        # 数据库表结构
```

## 数据库设计

### 核心数据表

| 表名 | 说明 | 主要字段 |
|-----|------|---------|
| user | 用户表 | id, phone, password, nickname, avatar |
| product | 产品表 | id, title, desc, icon, image_url, features |
| solution | 解决方案表 | id, title, desc, icon, tags, content |
| news | 新闻资讯表 | id, title, summary, content, cover_image, category |
| feedback | 用户反馈表 | id, user_id, content, contact |
| role | 角色表 | id, name, description, permissions |
| menu | 菜单表 | id, name, path, component, permission |
| meta_entity | 元数据实体表 | id, name, table_name, description |
| meta_field | 元数据字段表 | id, entity_id, field_name, field_type |

### 数据库特性
- 统一的审计字段：created_by, updated_by, created_at, updated_at
- 支持逻辑删除
- 字符集：UTF-8
- 时区：Asia/Shanghai

## API接口规范

### 认证接口

#### 用户注册
```http
POST /auth1/register
Content-Type: application/json

{
  "phone": "13800138000",
  "password": "password123",
  "confirmPassword": "password123", 
  "verificationCode": "123456",
  "nickname": "用户昵称",
  "avatar": "头像URL"
}
```

#### 用户登录
```http
POST /auth1/login
Content-Type: application/json

{
  "phone": "13800138000",
  "password": "password123"
}
```

### 响应格式
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {}
}
```

### 状态码规范
- 0: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 权限不足
- 500: 服务器内部错误

## 部署架构

### 容器化部署
- **容器技术**: Docker
- **编排工具**: Kubernetes
- **镜像仓库**: 私有Registry
- **配置管理**: Nacos Config
- **服务发现**: Nacos Discovery

### 监控体系
- **流量控制**: Sentinel Dashboard
- **服务监控**: Spring Boot Actuator
- **日志收集**: 统一日志格式
- **性能测试**: JMeter压测脚本

## 开发规范

### 代码规范
- Java代码遵循阿里巴巴Java开发手册
- 前端代码使用ESLint + Prettier
- 统一使用UTF-8编码
- 接口命名采用RESTful风格

### 分支管理
- main: 主分支，生产环境代码
- develop: 开发分支
- feature/*: 功能分支
- hotfix/*: 热修复分支

### 测试策略
- 单元测试覆盖率 > 70%
- 集成测试覆盖核心业务流程
- 压力测试验证系统性能
- 安全测试确保数据安全

## 项目特色

### 1. 微服务架构
- 服务拆分合理，职责清晰
- 支持独立部署和扩展
- 统一的服务治理

### 2. 低代码平台
- 可视化表单设计
- 自动代码生成
- 动态数据模型

### 3. 多端支持
- 微信小程序原生开发
- Vue3响应式Web管理端
- 统一的后端API服务

### 4. 完善的权限体系
- 基于RBAC的权限模型
- 动态菜单和路由
- 细粒度权限控制

### 5. 企业级特性
- 分布式架构
- 服务熔断降级
- 配置中心管理
- 监控告警体系

## 后续规划

### 短期目标（1-3个月）
- [ ] 完善单元测试覆盖
- [ ] 优化前端用户体验
- [ ] 增加数据统计分析功能
- [ ] 完善API文档

### 中期目标（3-6个月）
- [ ] 支持多租户架构
- [ ] 集成第三方支付
- [ ] 增加消息推送功能
- [ ] 移动端App开发

### 长期目标（6-12个月）
- [ ] 人工智能集成
- [ ] 大数据分析平台
- [ ] 国际化支持
- [ ] 开放平台建设

---

**文档版本**: 1.0  
**更新时间**: 2025-08-25  
**维护团队**: 腾讯云开发团队