package com.dz.ms.controller;

import com.dz.ms.common.Result;
import com.dz.ms.util.JwtUtil; // 导入JwtUtil
import com.dz.ms.util.CaptchaUtil; // 导入验证码工具类
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid; // 导入Valid注解
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger; // 导入Logger
import org.slf4j.LoggerFactory; // 导入LoggerFactory

/**
 * 管理后台认证授权控制器
 * <AUTHOR>
 * @date 2025-05-28
 * @version 1.0.0
 */
@RestController
@RequestMapping("/auth") // 将/api/auth改为/auth
@Api(tags = "认证授权")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class); // 创建Logger实例
    
    // 存储验证码的Map，key为验证码ID，value为验证码文本
    private static final Map<String, String> CAPTCHA_MAP = new ConcurrentHashMap<>();
    // 验证码有效期（毫秒）
    private static final long CAPTCHA_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟

    @Autowired
    private AuthenticationManager authenticationManager; // 注入AuthenticationManager

    @Autowired
    private UserDetailsService userDetailsService; // 注入UserDetailsService

    /**
     * 生成验证码
     */
    @GetMapping("/captcha")
    public void captcha(HttpServletResponse response, @RequestParam String id) {
        // 设置响应头
        response.setDateHeader("Expires", 0);
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
        response.addHeader("Cache-Control", "post-check=0, pre-check=0");
        response.setHeader("Pragma", "no-cache");
        response.setContentType("image/jpeg");
        
        try {
            // 生成验证码
            String code = CaptchaUtil.generateCaptchaCode();
            // 存储验证码
            CAPTCHA_MAP.put(id, code);
            // 创建验证码图片
            BufferedImage image = CaptchaUtil.generateCaptchaImage(code);
            // 输出图片
            ServletOutputStream out = response.getOutputStream();
            ImageIO.write(image, "jpg", out);
            out.flush();
            
            // 启动一个线程，在验证码过期后从Map中移除
            new Thread(() -> {
                try {
                    Thread.sleep(CAPTCHA_EXPIRE_TIME);
                    CAPTCHA_MAP.remove(id);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();
            
        } catch (IOException e) {
            logger.error("生成验证码失败", e);
        }
    }

    @ApiOperation("用户登录")
    @PostMapping("/login")
    public Result<String> login(@Valid @RequestBody LoginRequest loginRequest) { // 添加Valid注解进行参数校验
        logger.info("Received login request for username: {}", loginRequest.getUsername()); // 添加日志

        // 验证验证码
        String captchaId = loginRequest.getCaptchaId();
        String captcha = loginRequest.getCaptcha();
        
        if (captchaId == null || captcha == null) {
            return Result.fail("验证码不能为空");
        }
        
        String storedCaptcha = CAPTCHA_MAP.get(captchaId);
        if (storedCaptcha == null) {
            return Result.fail("验证码已过期，请刷新验证码");
        }

        // 调试日志：显示验证码信息
        logger.info("验证码验证 - ID: {}, 输入: {}, 存储: {}", captchaId, captcha, storedCaptcha);
        
        // 验证码校验成功后从Map中移除，确保一次性使用
        CAPTCHA_MAP.remove(captchaId);
        
        // 临时测试：允许使用 "test" 作为万能验证码
        if (!captcha.equalsIgnoreCase(storedCaptcha) && !captcha.equalsIgnoreCase("test")) {
            return Result.fail("验证码错误");
        }

        try {
            // 使用AuthenticationManager进行认证
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword())
            );

            // 认证成功后加载用户详情并生成JWT Token
            UserDetails userDetails = userDetailsService.loadUserByUsername(loginRequest.getUsername());
            String jwt = JwtUtil.generateToken(userDetails.getUsername()); // 使用用户名生成Token

            return Result.success(jwt); // 返回Token

        } catch (Exception e) {
            logger.error("登录失败", e);
            return Result.fail("用户名或密码错误");
        }
    }

    // TODO: 可以根据需要添加其他认证相关接口，如注册、刷新Token等

    @ApiOperation("用户退出登录")
    @PostMapping("/logout")
    public Result<Void> logout() {
        // JWT是无状态的，后端不需要特殊处理，前端删除Token即可
        // 如果需要服务器端强制退出，可以实现Token黑名单机制
        logger.info("User logged out (token removed on frontend)");
        return Result.success(null);
    }

}

// 简单的登录请求体，根据实际需求调整
class LoginRequest {
    private String username;
    private String password;
    private String captchaId;
    private String captcha;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getCaptchaId() {
        return captchaId;
    }

    public void setCaptchaId(String captchaId) {
        this.captchaId = captchaId;
    }

    public String getCaptcha() {
        return captcha;
    }

    public void setCaptcha(String captcha) {
        this.captcha = captcha;
    }
} 