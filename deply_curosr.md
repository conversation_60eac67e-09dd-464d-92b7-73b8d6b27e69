# AWX 本地部署指南（Windows）

适用环境：Windows 10/11（PowerShell），本机开发与验证。

## 1. 前置依赖
- JDK 1.8+（建议 Temurin 8/11）
- Maven 3.6+
- Node.js 16+（含 npm）
- MySQL 8.0+（本地或容器）
- Nacos 2.0+（单机）
- Sentinel Dashboard 1.8+
- Git（可选）

验证命令：
```powershell
java -version
mvn -v
node -v
npm -v
mysql --version
```

## 2. 初始化数据库
1) 启动 MySQL，创建数据库与表：
```powershell
mysql -uroot -p
```
在 MySQL 控制台执行项目脚本：
```sql
SOURCE D:/code/awx/database_schema.sql;
```
注意：将路径替换为你本机 `database_schema.sql` 的实际绝对路径。

2) 建议创建业务用户（可选）：
```sql
CREATE USER 'awx'@'%' IDENTIFIED BY 'Awx@123456';
GRANT ALL PRIVILEGES ON awx.* TO 'awx'@'%';
FLUSH PRIVILEGES;
```

## 3. 启动 Nacos（单机）
1) 下载并解压 Nacos（`nacos-server-2.x`）。
2) 使用内置 Derby 或配置到本地 MySQL（建议保留默认以便快速起步）。
3) 启动（PowerShell）：
```powershell
cd <nacos>/bin
startup.cmd -m standalone | cat
```
4) 访问控制台：`http://localhost:8848/nacos`（默认 `nacos/nacos`）。

5) 在 Nacos 导入 Sentinel 规则（来自项目 `nacos-configs/`）：
- 新建配置（Group: `SENTINEL_GROUP`）
  - Data ID: `gateway-service-gw-flow-rules` -> 内容为 `nacos-configs/gateway-service-gw-flow-rules.json`
  - Data ID: `gateway-service-gw-api-group-rules` -> 内容为 `nacos-configs/gateway-service-gw-api-group-rules.json`
  - Data ID: `backend-service-flow-rules` -> 内容为 `nacos-configs/backend-service-flow-rules.json`
  - Data ID: `backend-service-degrade-rules` -> 内容为 `nacos-configs/backend-service-degrade-rules.json`
  - Data ID: `backend-service-system-rules` -> 内容为 `nacos-configs/backend-service-system-rules.json`

## 4. 启动 Sentinel Dashboard
1) 下载 `sentinel-dashboard-1.8.x.jar`。
2) 启动（注意开放 8080 端口）：
```powershell
java -Dserver.port=8080 -Dcsp.sentinel.dashboard.server=localhost:8080 -jar sentinel-dashboard-1.8.x.jar
```
3) 登录地址：`http://localhost:8080`（默认 `sentinel/sentinel123`，如 README-Sentinel.md 所述）。

## 5. 后端服务启动（backend-service）
1) 安装依赖并编译：
```powershell
cd D:/code/awx/backend
mvn -q -DskipTests package
```
2) 配置应用（若使用 Nacos 配置，可直接启动；否则在本地 `application.yml` 中设置 DB 与 Nacos）。
3) 运行服务：
```powershell
cd backend/backend-service
mvn spring-boot:run
```
默认端口：查看日志或本地配置，一般为 `9091`（可在 Nacos 或资源配置中调整）。

注意：`backend-service` 依赖 Nacos（发现与配置）与 MySQL 可用。

## 6. 网关服务启动（gateway）
1) 编译并运行：
```powershell
cd D:/code/awx/backend/gateway
mvn spring-boot:run
```
默认网关端口：建议 `9090`。

路由规则参考 `nacos-configs/rtm-gateway.yaml`：
- `/api/**` 转发到后端服务（示例 `http://<backend-host>:9091`），并 `StripPrefix=1`。

确保在 Nacos 中有对应的 `rtm-gateway.yaml` 配置（或本地 `application.yml` 中等价配置），并指向你的本机地址：
```yaml
spring:
  cloud:
    gateway:
      routes:
        - id: backend-service
          uri: http://localhost:9091
          predicates:
            - Path=/api/**
          filters:
            - StripPrefix=1
            - AddRequestHeader=X-Gateway-Source, rtm-gateway
    nacos:
      discovery:
        server-addr: localhost:8848
```

## 7. 前端（frontend）
1) 安装依赖：
```powershell
cd D:/code/awx/frontend
npm ci
```
2) 本地开发启动：
- 推荐在 `vite.config.ts` 中启用代理到网关（已存在示例，需取消注释并指向 `http://localhost:9090`）：
```ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'

export default defineConfig({
  plugins: [vue()],
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:9090',
        changeOrigin: true
      }
    }
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  }
})
```
3) 启动：
```powershell
npm run dev
```
访问：`http://localhost:5173`（或控制台显示端口）。

4) 生产构建（可选）：
```powershell
npm run build
```

## 8. 小程序（miniapp）
1) 使用微信开发者工具打开 `D:/code/awx/miniapp`。
2) 在 `app.json` 与各页面请求中，将后端网关地址统一指向 `http://localhost:9090` 的 `/api` 前缀接口（如果有直连后端的请求，建议改为走网关）。
3) 运行预览与调试。

## 9. 验证与联调
- 后端健康检查：
  - 访问后端 Swagger/OpenAPI（若启用）：`http://localhost:9091/swagger-ui.html` 或 `/swagger-ui/index.html`
- 网关转发测试：
  - `GET http://localhost:9090/api/health`（示例）
- 前端接口：
  - 前端页面登录、菜单加载、用户管理等应能通过 `/api` 访问网关后端
- Sentinel：
  - `http://localhost:8080` 查看实时流控与规则
- Nacos：
  - `http://localhost:8848/nacos` 查看服务注册与配置

## 10. 常见问题（FAQ）
- 端口占用：修改 `application.yml` 或 Vite 配置端口，或释放端口。
- Nacos 连接失败：检查 `server-addr` 与本机网络；Windows 防火墙放通 8848。
- 数据库连接失败：确认 MySQL 账号密码、库名 `awx` 与驱动版本（8.0+）。
- 接口 401/403：检查 Spring Security 与 JWT 配置，确认前端是否携带 Token。
- 网关 404：确认路由 `Path=/api/**` 与 `StripPrefix=1` 是否生效，后端服务地址是否可达。
- Sentinel 规则不生效：检查 Nacos 中对应 `SENTINEL_GROUP` 下规则数据是否同步。

## 11. 端口与服务清单（建议）
- Nacos：8848
- Sentinel Dashboard：8080
- Gateway：9090
- Backend-Service：9091
- Frontend Dev：5173（Vite 默认）
- MySQL：3306

---
以上步骤完成后，你应能在本地完整跑通：前端 -> 网关 -> 后端 -> MySQL，并通过 Nacos/Sentinel 进行配置与监控。
